# Web管理后台菜单配置说明

## 菜单结构

已为案例管理系统添加了完整的菜单配置，菜单结构如下：

```
案例管理 (案例管理目录)
├── 案例用户 (案例用户管理)
└── 案例信息 (案例信息管理)
```

## 菜单详细配置

### 1. 一级菜单 - 案例管理
- **菜单ID**: 5
- **菜单名称**: 案例管理
- **路由地址**: case
- **菜单图标**: example
- **菜单类型**: 目录(M)
- **显示顺序**: 5

### 2. 二级菜单 - 案例用户
- **菜单ID**: 2000
- **菜单名称**: 案例用户
- **路由地址**: caseUser
- **组件路径**: system/caseUser/index
- **菜单图标**: user
- **菜单类型**: 菜单(C)
- **权限标识**: system:caseUser:list

#### 案例用户管理按钮权限
- **查询权限** (2100): system:caseUser:query
- **新增权限** (2101): system:caseUser:add
- **修改权限** (2102): system:caseUser:edit
- **删除权限** (2103): system:caseUser:remove
- **导出权限** (2104): system:caseUser:export
- **导入权限** (2105): system:caseUser:import

### 3. 二级菜单 - 案例信息
- **菜单ID**: 2001
- **菜单名称**: 案例信息
- **路由地址**: caseInfo
- **组件路径**: system/caseInfo/index
- **菜单图标**: documentation
- **菜单类型**: 菜单(C)
- **权限标识**: system:caseInfo:list

#### 案例信息管理按钮权限
- **查询权限** (2200): system:caseInfo:query
- **新增权限** (2201): system:caseInfo:add
- **修改权限** (2202): system:caseInfo:edit
- **删除权限** (2203): system:caseInfo:remove
- **导出权限** (2204): system:caseInfo:export
- **导入权限** (2205): system:caseInfo:import

## 角色权限分配

### 管理员角色 (role_id=1)
拥有案例管理的所有权限：
- 案例管理目录访问权限
- 案例用户的完整CRUD权限
- 案例信息的完整CRUD权限
- 数据导入导出权限

### 普通角色 (role_id=2)
拥有有限的权限：
- 案例管理目录访问权限
- 案例用户的查询权限
- 案例信息的查询权限

## 前端页面路径

菜单配置对应的前端页面文件路径：

```
case-api/ruoyi-ui/src/views/system/
├── caseUser/
│   └── index.vue          # 案例用户管理页面
└── caseInfo/
    └── index.vue          # 案例信息管理页面
```

## API接口路径

菜单功能对应的后端API接口：

```
案例用户管理：
- GET    /system/caseUser/list      # 查询列表
- GET    /system/caseUser/{id}      # 查询详情
- POST   /system/caseUser           # 新增
- PUT    /system/caseUser           # 修改
- DELETE /system/caseUser/{ids}     # 删除
- POST   /system/caseUser/export    # 导出
- POST   /system/caseUser/importData # 导入

案例信息管理：
- GET    /system/caseInfo/list      # 查询列表
- GET    /system/caseInfo/{id}      # 查询详情
- POST   /system/caseInfo           # 新增
- PUT    /system/caseInfo           # 修改
- DELETE /system/caseInfo/{ids}     # 删除
- POST   /system/caseInfo/export    # 导出
- POST   /system/caseInfo/importData # 导入
```

## 使用说明

### 1. 数据库初始化
执行数据库脚本 `case-api/sql/ry_20250522.sql`，该脚本包含：
- 菜单数据初始化
- 角色权限分配
- 示例数据创建

### 2. 系统启动
1. 启动后端服务
2. 启动前端服务
3. 使用管理员账号登录：admin/admin123

### 3. 菜单访问
登录后在左侧导航栏可以看到"案例管理"菜单，展开后包含：
- 案例用户：管理案例发布用户
- 案例信息：管理案例内容信息

### 4. 权限验证
- 管理员可以看到所有操作按钮
- 普通用户只能看到查询相关功能
- 无权限用户无法访问案例管理菜单

## 菜单图标说明

使用的图标都是若依框架自带的SVG图标：
- `example`: 案例管理目录图标
- `user`: 案例用户管理图标
- `documentation`: 案例信息管理图标

图标文件位置：`case-api/ruoyi-ui/src/assets/icons/svg/`

## 自定义配置

### 修改菜单顺序
在数据库中修改 `sys_menu` 表的 `order_num` 字段

### 修改菜单图标
1. 在 `case-api/ruoyi-ui/src/assets/icons/svg/` 目录添加新的SVG图标
2. 修改数据库中 `sys_menu` 表的 `icon` 字段

### 添加新的权限
1. 在 `sys_menu` 表中添加新的按钮权限记录
2. 在 `sys_role_menu` 表中为相应角色分配权限
3. 在前端页面中使用 `v-hasPermi` 指令控制按钮显示

### 修改角色权限
在 `sys_role_menu` 表中添加或删除角色与菜单的关联关系

## 注意事项

1. **菜单ID唯一性**: 确保菜单ID在系统中唯一
2. **权限标识规范**: 权限标识建议使用 `模块:功能:操作` 的格式
3. **路由地址**: 前端路由地址要与组件路径对应
4. **角色权限**: 修改权限后需要重新登录才能生效
5. **菜单缓存**: 若依框架会缓存菜单数据，修改后可能需要清除缓存

## 故障排除

### 菜单不显示
1. 检查用户是否有对应的角色权限
2. 检查菜单状态是否为正常
3. 检查前端组件路径是否正确

### 按钮权限无效
1. 检查按钮权限是否正确配置
2. 检查前端页面是否使用了权限指令
3. 检查用户角色是否有对应的按钮权限

### 页面访问404
1. 检查前端组件文件是否存在
2. 检查路由配置是否正确
3. 检查组件路径大小写是否匹配
