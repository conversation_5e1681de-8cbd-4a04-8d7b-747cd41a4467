-- 验证案例管理菜单配置的SQL脚本

-- 1. 查看案例管理相关菜单
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    menu_type,
    visible,
    status,
    perms,
    icon
FROM sys_menu 
WHERE menu_id IN (5, 2000, 2001) 
   OR parent_id IN (5, 2000, 2001)
ORDER BY parent_id, order_num;

-- 2. 查看管理员角色的案例管理权限
SELECT 
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.menu_type,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id = 1 
  AND (m.menu_id IN (5, 2000, 2001) OR m.parent_id IN (5, 2000, 2001))
ORDER BY m.menu_id;

-- 3. 查看普通角色的案例管理权限
SELECT 
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.menu_type,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id = 2 
  AND (m.menu_id IN (5, 2000, 2001) OR m.parent_id IN (5, 2000, 2001))
ORDER BY m.menu_id;

-- 4. 验证菜单层级结构
SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('├── ', menu_name)
        WHEN parent_id > 0 AND menu_type = 'C' THEN CONCAT('│   ├── ', menu_name)
        WHEN parent_id > 0 AND menu_type = 'F' THEN CONCAT('│   │   ├── ', menu_name)
        ELSE menu_name
    END AS menu_structure,
    menu_id,
    parent_id,
    menu_type,
    path,
    component
FROM sys_menu 
WHERE menu_id = 5 
   OR parent_id = 5 
   OR parent_id IN (2000, 2001)
ORDER BY 
    CASE WHEN parent_id = 0 THEN menu_id END,
    CASE WHEN parent_id = 5 THEN menu_id END,
    CASE WHEN parent_id IN (2000, 2001) THEN parent_id END,
    menu_id;

-- 5. 检查是否有重复的菜单ID
SELECT menu_id, COUNT(*) as count
FROM sys_menu 
WHERE menu_id IN (5, 2000, 2001, 2100, 2101, 2102, 2103, 2104, 2105, 2200, 2201, 2202, 2203, 2204, 2205)
GROUP BY menu_id
HAVING COUNT(*) > 1;

-- 6. 检查权限标识是否规范
SELECT 
    menu_id,
    menu_name,
    perms,
    CASE 
        WHEN perms = '' OR perms IS NULL THEN '无权限标识'
        WHEN perms NOT LIKE '%:%:%' AND menu_type = 'F' THEN '权限标识格式不规范'
        ELSE '正常'
    END as perms_status
FROM sys_menu 
WHERE menu_id IN (5, 2000, 2001, 2100, 2101, 2102, 2103, 2104, 2105, 2200, 2201, 2202, 2203, 2204, 2205);

-- 7. 检查菜单状态
SELECT 
    menu_id,
    menu_name,
    visible,
    status,
    CASE 
        WHEN visible = '1' THEN '隐藏'
        WHEN status = '1' THEN '停用'
        ELSE '正常'
    END as menu_status
FROM sys_menu 
WHERE menu_id IN (5, 2000, 2001, 2100, 2101, 2102, 2103, 2104, 2105, 2200, 2201, 2202, 2203, 2204, 2205);

-- 8. 验证角色权限完整性
-- 检查管理员是否有完整权限
SELECT 
    '管理员权限检查' as check_type,
    CASE 
        WHEN COUNT(*) = 16 THEN '完整'
        ELSE CONCAT('缺失 ', 16 - COUNT(*), ' 个权限')
    END as result
FROM sys_role_menu 
WHERE role_id = 1 
  AND menu_id IN (5, 2000, 2001, 2100, 2101, 2102, 2103, 2104, 2105, 2200, 2201, 2202, 2203, 2204, 2205)

UNION ALL

-- 检查普通角色是否有基础权限
SELECT 
    '普通角色权限检查' as check_type,
    CASE 
        WHEN COUNT(*) = 5 THEN '完整'
        ELSE CONCAT('缺失 ', 5 - COUNT(*), ' 个权限')
    END as result
FROM sys_role_menu 
WHERE role_id = 2 
  AND menu_id IN (5, 2000, 2001, 2100, 2200);
