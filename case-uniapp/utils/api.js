// API配置
const BASE_URL = 'http://localhost:7788'

// 请求封装
const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data)
          } else {
            uni.showToast({
              title: res.data.msg || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else {
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// 案例用户相关API
export const caseUserApi = {
  // 获取优秀客户列表
  getExcellentUsers() {
    return request({
      url: '/system/caseUser/mobile/excellent'
    })
  },
  
  // 获取用户详情
  getUserDetail(userId) {
    return request({
      url: `/system/caseUser/mobile/${userId}`
    })
  }
}

// 案例信息相关API
export const caseInfoApi = {
  // 获取推荐案例列表
  getRecommendedCases(limit = 10) {
    return request({
      url: '/system/caseInfo/mobile/recommended',
      data: { limit }
    })
  },
  
  // 获取最新案例列表
  getLatestCases(limit = 10) {
    return request({
      url: '/system/caseInfo/mobile/latest',
      data: { limit }
    })
  },
  
  // 根据发布人ID获取案例列表
  getCasesByPublisher(publisherId) {
    return request({
      url: `/system/caseInfo/mobile/publisher/${publisherId}`
    })
  },
  
  // 根据标签获取案例列表
  getCasesByTag(tag) {
    return request({
      url: `/system/caseInfo/mobile/tag/${tag}`
    })
  },
  
  // 获取案例详情
  getCaseDetail(caseId) {
    return request({
      url: `/system/caseInfo/mobile/${caseId}`
    })
  },
  
  // 增加点击次数
  incrementClick(caseId) {
    return request({
      url: `/system/caseInfo/mobile/click/${caseId}`,
      method: 'POST'
    })
  }
}

export default request
